import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import AnimatedFloatingBlocks from '@/components/AnimatedFloatingBlocks'

const NotFound = () => {
  const location = useLocation()

  useEffect(() => {
    console.error(
      '404 Error: User attempted to access non-existent route:',
      location.pathname
    )
  }, [location.pathname])

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden">
      <Navbar />

      {/* 404 Hero Section */}
      <div className="min-h-screen flex items-center justify-center bg-gradient-overlay relative overflow-hidden">
        {/* Animated Floating Blocks */}
        <AnimatedFloatingBlocks />

        <div className="text-center relative z-10">
          <h1 className="text-8xl md:text-9xl font-bold mb-4 text-agency-green">
            404
          </h1>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
            Page Not Found
          </h2>
          <p className="text-xl text-agency-white-muted mb-8 max-w-md mx-auto">
            Oops! The page you're looking for doesn't exist. Let's get you back
            on track.
          </p>
          <a
            href="/"
            className="inline-flex px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            Return to Home
          </a>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default NotFound;
