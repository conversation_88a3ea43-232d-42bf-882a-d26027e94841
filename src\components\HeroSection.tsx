
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';

const HeroSection = () => {
  const headingRef = useRef<HTMLHeadingElement>(null);
  const subheadingRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline();

    tl.fromTo(
      headingRef.current,
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
    )
      .fromTo(
        subheadingRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power3.out" },
        "-=0.4"
      )
      .fromTo(
        buttonsRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: "power3.out" },
        "-=0.2"
      );

    return () => {
      tl.kill();
    };
  }, []);

  return (
    <section
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-agency-dark"
      ref={containerRef}
    >
      <div className="absolute inset-0 bg-gradient-overlay"></div>

      <div className="relative z-10 container mx-auto px-4 md:px-6 pt-24 pb-16 text-center">
        <h1
          ref={headingRef}
          className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 max-w-5xl mx-auto leading-tight text-white"
        >
          A Digital Product Studio
          <br />
          <span className="text-agency-green">that will Work</span>
        </h1>

        <p
          ref={subheadingRef}
          className="text-agency-white-muted text-lg md:text-xl max-w-2xl mx-auto mb-8"
        >
          We design and build digital products that help businesses solve
          complex problems and achieve their goals
        </p>

        <div
          ref={buttonsRef}
          className="flex flex-col sm:flex-row items-center justify-center gap-4"
        >
          <a
            href="#services"
            className="px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            Get Started
          </a>
          <a
            href="#work"
            className="px-8 py-3 border border-white/20 text-white hover:bg-white/10 font-medium rounded-md transition-all"
          >
            See Our Work
          </a>
        </div>
      </div>

      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce z-20">
        <svg
          className="w-8 h-8 text-agency-white-muted"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </div>
    </section>
  )
};

export default HeroSection;
