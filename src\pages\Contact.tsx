
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Mail, Phone, MapPin } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';
import FaqSection from '@/components/FaqSection';
import { Button } from '@/components/ui/button';

gsap.registerPlugin(ScrollTrigger);

const Contact = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const heroTl = gsap.timeline();
    heroTl.fromTo(
      heroRef.current?.querySelector('h1'),
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8 }
    );
    heroTl.fromTo(
      heroRef.current?.querySelector('p'),
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.8 },
      "-=0.6"
    );

    gsap.fromTo(
      infoRef.current?.querySelectorAll('.contact-info-item'),
      { opacity: 0, y: 20 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.6, 
        stagger: 0.2,
        scrollTrigger: {
          trigger: infoRef.current,
          start: "top 80%",
        }
      }
    );

    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden">
      <Navbar />
      
      {/* Hero Section */}
      <div 
        ref={heroRef}
        className="bg-gradient-overlay py-24 md:py-32 text-center relative"
      >
        <div className="container mx-auto px-4">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">Contact Us</h1>
          <p className="text-agency-white-muted max-w-2xl mx-auto">
            Get in touch with us today and let us help you with any questions or inquiries you may have.
          </p>
        </div>
      </div>

      {/* Contact Info Section */}
      <div 
        ref={infoRef}
        className="py-12 bg-agency-dark"
      >
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-center gap-8 mb-12">
            <a href="mailto:<EMAIL>" className="contact-info-item flex items-center justify-center gap-3 bg-agency-darker p-5 rounded-md border border-white/5 hover:border-agency-green/30 transition-all">
              <Mail className="text-agency-green" size={20} />
              <span><EMAIL></span>
            </a>
            <a href="tel:+919123232308" className="contact-info-item flex items-center justify-center gap-3 bg-agency-darker p-5 rounded-md border border-white/5 hover:border-agency-green/30 transition-all">
              <Phone className="text-agency-green" size={20} />
              <span>+91 9123 23 2308</span>
            </a>
            <a href="#" className="contact-info-item flex items-center justify-center gap-3 bg-agency-darker p-5 rounded-md border border-white/5 hover:border-agency-green/30 transition-all">
              <MapPin className="text-agency-green" size={20} />
              <span>Get Location</span>
            </a>
          </div>

          {/* Contact Form Section */}
          <ContactForm />
        </div>
      </div>

      {/* Operating Days */}
      <div className="py-8 bg-agency-darker">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0 flex items-center gap-8">
              <div>
                <p className="text-agency-white-muted">Operating Days</p>
                <p className="font-medium">Monday to Friday</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-agency-white-muted mr-2">Stay Connected</span>
              <a href="#" className="text-white hover:text-agency-green p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
              </a>
              <a href="#" className="text-white hover:text-agency-green p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
              </a>
              <a href="#" className="text-white hover:text-agency-green p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-gradient-overlay py-16 md:py-24 text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-3">Frequently Asked Questions</h2>
          <p className="text-agency-white-muted mb-6">Still you have any question? Contact our <NAME_EMAIL></p>
        </div>
      </div>

      <FaqSection />

      {/* Company Info Section */}
      <div className="bg-agency-darker py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
            <div className="flex items-start gap-4">
              <div className="bg-agency-green p-3 rounded">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-agency-dark"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-3">Today, SquareUp Continues to Thrive as a Leading Digital Product Agency....</h3>
                <p className="text-agency-white-muted text-sm">
                  Combining the power of design, engineering, and project management to create transformative digital experiences. They invite you to join them on their journey and discover how they can help bring your digital ideas to life.
                </p>
              </div>
            </div>
            <div className="md:col-span-1">
              <p className="text-agency-white-muted">Welcome to SquareUp</p>
              <p>Where collaboration, Expertise, and Client-Centricity Intersect to Shape the Future of Digital Innovation.</p>
            </div>
            <div className="flex justify-center md:justify-end">
              <Button className="bg-agency-green text-agency-dark hover:bg-agency-green/90 px-6 py-6">
                Start Project
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Contact;
