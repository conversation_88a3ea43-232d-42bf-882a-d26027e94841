
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';

const HeroSection = () => {
  const headingRef = useRef<HTMLHeadingElement>(null);
  const subheadingRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const arrowRef = useRef<HTMLButtonElement>(null)
  const animatedBlocksRef = useRef<HTMLDivElement>(null)

  const handleScrollToNext = () => {
    const nextSection = document.getElementById('clients')
    if (nextSection) {
      // Add a cool animation to the arrow before scrolling
      gsap.to(arrowRef.current, {
        scale: 0.8,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: 'power2.inOut'
      })

      // Smooth scroll to the next section
      nextSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  useEffect(() => {
    const tl = gsap.timeline()

    tl.fromTo(
      headingRef.current,
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: 'power3.out' }
    )
      .fromTo(
        subheadingRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out' },
        '-=0.4'
      )
      .fromTo(
        buttonsRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out' },
        '-=0.2'
      )

    // Animate floating blocks
    if (animatedBlocksRef.current) {
      const blocks = animatedBlocksRef.current.children

      Array.from(blocks).forEach((block, index) => {
        // Random initial positions
        const randomX = Math.random() * window.innerWidth
        const randomY = Math.random() * window.innerHeight

        gsap.set(block, {
          x: randomX,
          y: randomY,
          rotation: Math.random() * 360
        })

        // Create infinite floating animation
        gsap.to(block, {
          x: `+=${Math.random() * 200 - 100}`,
          y: `+=${Math.random() * 200 - 100}`,
          rotation: `+=${Math.random() * 180 - 90}`,
          duration: 8 + Math.random() * 4,
          ease: 'none',
          repeat: -1,
          yoyo: true,
          delay: index * 0.5
        })

        // Add subtle scale pulsing
        gsap.to(block, {
          scale: 1.1,
          duration: 3 + Math.random() * 2,
          ease: 'power2.inOut',
          repeat: -1,
          yoyo: true,
          delay: index * 0.3
        })
      })
    }

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <section
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-agency-dark"
      ref={containerRef}
    >
      <div className="absolute inset-0 bg-gradient-overlay"></div>

      {/* Animated Floating Blocks */}
      <div
        ref={animatedBlocksRef}
        className="absolute inset-0 pointer-events-none"
      >
        {/* Geometric Block 1 - Square */}
        <div className="absolute w-8 h-8 bg-agency-green/10 border border-agency-green/20 backdrop-blur-sm floating-block glow-effect"></div>

        {/* Geometric Block 2 - Diamond */}
        <div className="absolute w-6 h-6 bg-agency-green/15 border border-agency-green/30 rotate-45 backdrop-blur-sm floating-block glow-effect"></div>

        {/* Geometric Block 3 - Rectangle */}
        <div className="absolute w-12 h-4 bg-agency-green/8 border border-agency-green/15 backdrop-blur-sm floating-block glow-effect"></div>

        {/* Geometric Block 4 - Circle */}
        <div className="absolute w-10 h-10 bg-agency-green/12 border border-agency-green/25 rounded-full backdrop-blur-sm floating-block glow-effect"></div>

        {/* Geometric Block 5 - Triangle */}
        <div className="absolute w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-agency-green/20 glow-effect"></div>

        {/* Geometric Block 6 - Hexagon */}
        <div
          className="absolute w-8 h-8 bg-agency-green/10 border border-agency-green/20 backdrop-blur-sm floating-block glow-effect"
          style={{
            clipPath:
              'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)'
          }}
        ></div>

        {/* Geometric Block 7 - Small Square */}
        <div className="absolute w-4 h-4 bg-agency-green/20 border border-agency-green/40 backdrop-blur-sm floating-block glow-effect"></div>

        {/* Geometric Block 8 - Oval */}
        <div className="absolute w-16 h-8 bg-agency-green/8 border border-agency-green/15 rounded-full backdrop-blur-sm floating-block glow-effect"></div>

        {/* Geometric Block 9 - Pentagon */}
        <div
          className="absolute w-6 h-6 bg-agency-green/15 border border-agency-green/30 backdrop-blur-sm floating-block glow-effect"
          style={{
            clipPath: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)'
          }}
        ></div>

        {/* Geometric Block 10 - Line */}
        <div className="absolute w-20 h-0.5 bg-gradient-to-r from-transparent via-agency-green/30 to-transparent glow-effect"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 md:px-6 pt-24 pb-16 text-center">
        <h1
          ref={headingRef}
          className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 max-w-5xl mx-auto leading-tight text-white"
        >
          A Digital Product Studio
          <br />
          <span className="text-agency-green">that will Work</span>
        </h1>

        <p
          ref={subheadingRef}
          className="text-agency-white-muted text-lg md:text-xl max-w-2xl mx-auto mb-8"
        >
          We design and build digital products that help businesses solve
          complex problems and achieve their goals
        </p>

        <div
          ref={buttonsRef}
          className="flex flex-col sm:flex-row items-center justify-center gap-4 w-full"
        >
          <a
            href="#services"
            className="px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow text-center w-full sm:w-auto max-w-xs"
          >
            Get Started
          </a>
          <a
            href="#work"
            className="px-8 py-3 border border-white/20 text-white hover:bg-white/10 font-medium rounded-md transition-all text-center w-full sm:w-auto max-w-xs"
          >
            See Our Work
          </a>
        </div>
      </div>

      {/* Clickable Arrow positioned at the very bottom of the section */}
      <button
        ref={arrowRef}
        onClick={handleScrollToNext}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20 flex items-center justify-center w-12 h-12 rounded-full hover:bg-white/10 transition-all duration-300 cursor-pointer group"
        aria-label="Scroll to next section"
      >
        <svg
          className="w-8 h-8 text-agency-white-muted group-hover:text-agency-green transition-colors duration-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </button>
    </section>
  )
};

export default HeroSection;
