
import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const ClientsSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headingRef = useRef<HTMLHeadingElement>(null);
  const logoContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top 80%",
      }
    });

    tl.fromTo(
      headingRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 }
    ).fromTo(
      logoContainerRef.current?.children,
      { y: 30, opacity: 0 },
      { y: 0, opacity: 1, stagger: 0.1, duration: 0.5 },
      "-=0.3"
    );

    return () => {
      if (tl.scrollTrigger) {
        tl.scrollTrigger.kill();
      }
      tl.kill();
    };
  }, []);

  const clients = [
    { name: 'Figma', logo: 'figma-logo.svg' },
    { name: 'Spotify', logo: 'spotify-logo.svg' },
    { name: 'Zoom', logo: 'zoom-logo.svg' },
    { name: 'Slack', logo: 'slack-logo.svg' },
    { name: 'Amazon', logo: 'amazon-logo.svg' },
    { name: 'Adobe', logo: 'adobe-logo.svg' },
  ];

  return (
    <section 
      ref={sectionRef}
      className="bg-agency-darker py-16 overflow-hidden"
    >
      <div className="container mx-auto px-4 md:px-6 text-center">
        <h3 
          ref={headingRef}
          className="text-sm uppercase tracking-wider text-agency-white-muted mb-10"
        >
          TRUSTED BY MARKET LEADERS
        </h3>
        
        <div 
          ref={logoContainerRef}
          className="flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16"
        >
          {clients.map((client, index) => (
            <div 
              key={index} 
              className="w-24 h-12 md:w-28 md:h-14 flex items-center justify-center grayscale hover:grayscale-0 opacity-70 hover:opacity-100 transition-all duration-300"
            >
              <div className="w-full h-full bg-white/10 flex items-center justify-center rounded">
                <span className="text-white/80 text-sm">{client.name}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ClientsSection;
