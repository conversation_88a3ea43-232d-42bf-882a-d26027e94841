
import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';

const AboutHero = () => {
  const titleRef = useRef<HTMLHeadingElement>(null);
  const descRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const tl = gsap.timeline();
    
    tl.fromTo(
      titleRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8, ease: "power3.out" }
    ).fromTo(
      descRef.current,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.7, ease: "power3.out" },
      "-=0.4"
    );
    
    return () => {
      tl.kill();
    };
  }, []);

  return (
    <section className="relative py-28 bg-gradient-overlay overflow-hidden">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-agency-dark/70 via-agency-dark/90 to-agency-dark"></div>
      </div>
      <div className="container mx-auto px-4 relative z-10 text-center">
        <h1
          ref={titleRef}
          className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white"
        >
          About Us
        </h1>
        <p
          ref={descRef}
          className="text-agency-white-muted max-w-3xl mx-auto text-lg"
        >
          Welcome to SquareUp, where collaboration, expertise, and
          client-centricity intersect to shape the future of digital innovation.
        </p>
      </div>
    </section>
  )
};

export default AboutHero;
